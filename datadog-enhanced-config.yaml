# Add this to your existing /etc/datadog-agent/datadog.yaml

# Set a meaningful hostname (change this per server)
hostname: thankview-web-01  # Examples: thankview-web-01, thankview-video-01, thankview-send-01

# Process monitoring
process_config:
  process_collection:
    enabled: true

# APM Configuration
apm_config:
  enabled: true
  env: prod

# PHP-specific configuration
php_config:
  enabled: true

# Log collection (optional but recommended)
logs_enabled: true

# Enhanced system metrics
system_probe_config:
  enabled: true

# Custom tags for all metrics
tags:
  - env:prod
  - service:thankview
  - role:web-server  # Change this per server type: web-server, video-server, send-server
