
- name: Install rdkafka for PHP 8.1
  hosts: aws_ec2
  become: 'yes'
  tasks:
    - name: Check if rdkafka is already installed
      command: php -m
      register: php_modules
      changed_when: false
      ignore_errors: yes  # Add this to prevent failure when PHP is not installed
    
    - name: Set fact based on rdkafka existence
      set_fact:
        php_exists: "{{ php_modules.rc is defined and php_modules.rc == 0 }}"
        rdkafka_exists: "{{ 'rdkafka' in php_modules.stdout if php_modules.rc is defined and php_modules.rc == 0 else false }}"
    
    - name: Display rdkafka installation status
      debug:
        msg: >-
          {% if php_exists %}
          rdkafka is {{ 'already installed' if rdkafka_exists else 'not installed' }}
          {% else %}
          PHP is not installed on this server, skipping rdkafka configuration
          {% endif %}
    
    # Only proceed with reinstallation if needed and PHP exists
    - name: Process rdkafka installation
      when: php_exists and not rdkafka_exists
      block:
        - name: Remove all existing rdkafka configuration files
          file:
            path: '{{ item }}'
            state: absent
          loop:
            - /etc/php/8.1/cli/conf.d/20-rdkafka.ini
            - /etc/php/8.1/fpm/conf.d/20-rdkafka.ini
            - /etc/php/8.1/cli/conf.d/30-rdkafka.ini
            - /etc/php/8.1/fpm/conf.d/30-rdkafka.ini
        
        - name: Uninstall existing rdkafka PECL extension
          command: pecl uninstall rdkafka
          ignore_errors: 'yes'
        
        - name: Remove PHP rdkafka package
          apt:
            name: php8.1-rdkafka
            state: absent
            purge: 'yes'
          ignore_errors: 'yes'
        
        - name: Find any leftover rdkafka.so files
          find:
            paths: /usr/lib/php
            patterns: '*rdkafka*'
            recurse: 'yes'
          register: leftover_files
        
        - name: Remove any leftover rdkafka.so files
          file:
            path: '{{ item.path }}'
            state: absent
          with_items: '{{ leftover_files.files }}'
          when: leftover_files.matched > 0
        
        - name: Update apt package list
          apt:
            update_cache: 'yes'
        
        - name: Install PHP development packages and librdkafka dependencies
          apt:
            name:
              - php8.1-dev
              - librdkafka-dev
            state: present
        
        - name: Install php8.1-rdkafka package
          apt:
            name: php8.1-rdkafka
            state: present
        
        - name: Create rdkafka configuration files
          copy:
            dest: '{{ item }}'
            content: extension=rdkafka.so
            mode: '0644'
          loop:
            - /etc/php/8.1/cli/conf.d/20-rdkafka.ini
            - /etc/php/8.1/fpm/conf.d/20-rdkafka.ini
        
        - name: Restart PHP-FPM
          systemd:
            name: php8.1-fpm
            state: restarted
            
    # New task to fetch and update .env files from AWS Parameter Store
    - name: Overwrite .env files from SSM via lookup
      block:
        - name: Fetch the full .env blob from Parameter Store
          set_fact:
            env_content: "{{ lookup(
              'aws_ssm',
              '/thankview-stage/thankview-env',
              region='us-east-1',
              decrypt=true
              ) + '\n' }}" # Add newline character here
        
        - name: Fetch the API-specific .env blob from Parameter Store
          set_fact:
            api_env_content: "{{ lookup(
              'aws_ssm',
              '/thankview-stage/thankview-api-env',
              region='us-east-1',
              decrypt=true
              ) + '\n' }}" # Add newline character here
        
        - name: Define list of .env paths
          set_fact:
            env_file_paths:
              - /var/www/thank-views/.env
              - /var/www/ThankView-API/.env
              - /var/www/ThankView-Envelope-Builder/.env
              - /var/www/thank-views-ca/.env
        
        - name: Stat parent directories of each .env file
          stat:
            path: "{{ item | dirname }}"
          loop: "{{ env_file_paths }}"
          register: env_stats
          loop_control:
            label: "{{ item }}"
          ignore_errors: yes
        
        - name: Overwrite existing .env files with the SSM blob
          copy:
            dest: "{{ item.item }}"
            content: "{% if item.item == '/var/www/ThankView-API/.env' %}{{ api_env_content }}{% else %}{{ env_content }}{% endif %}"
            owner: www-data
            group: www-data
            mode: '0644'
          loop: "{{ env_stats.results }}"
          when: item.stat.exists and item.stat.isdir
          loop_control:
            label: "{{ item.item }}"

    # Task to set APP_ROLE based on instance tags
    - name: Set APP_ROLE based on server type
      block:
        - name: Check if APP_ROLE already exists in .env file
          shell: grep -c "^APP_ROLE=" /var/www/thank-views/.env || true
          register: app_role_exists
          changed_when: false

        - name: Gather EC2 facts to get instance tags
          ec2_metadata_facts:
          register: ec2_facts

        - name: Get instance ID
          set_fact:
            instance_id: "{{ ec2_facts.ansible_facts.ansible_ec2_instance_id }}"
          when: ec2_facts is defined

        - name: Install Python pip
          apt:
            name: python3-pip
            state: present
            update_cache: yes
          
        - name: Install required Python modules for AWS operations
          pip:
            name:
              - boto3
              - botocore
            state: present
            executable: pip3
            extra_args: "--system"  # Install system-wide

        - name: Describe EC2 instance to get tags
          ec2_instance_info:
            instance_ids: "{{ instance_id }}"
            region: "{{ ec2_facts.ansible_facts.ansible_ec2_placement_region }}"
          register: instance_info
          vars:
            ansible_python_interpreter: /usr/bin/python3  # Explicitly set Python interpreter
          when: instance_id is defined

        - name: Extract APP_ROLE from tags
          set_fact:
            app_role_value: "{{ item.value }}"
          loop: "{{ instance_info.instances[0].tags | dict2items }}"  # Fixed loop using dict2items
          when:
            - instance_info is defined
            - instance_info.instances is defined
            - instance_info.instances | length > 0
            - item.key == 'APP_ROLE'

        - name: Debug APP_ROLE value from tags
          debug:
            msg: "Found APP_ROLE tag with value: {{ app_role_value }}"
          when: app_role_value is defined and app_role_value != ''

        - name: Set APP_ROLE in .env file from instance tag
          lineinfile:
            path: /var/www/thank-views/.env
            regexp: '^APP_ROLE='
            line: "APP_ROLE={{ app_role_value }}"
            state: present
          when:
            - app_role_exists.stdout == "0" or app_role_exists.stdout == "1"
            - app_role_value is defined
            - app_role_value != ''

        - name: Set Datadog hostname based on server role
          lineinfile:
            path: /etc/datadog-agent/datadog.yaml
            regexp: '^hostname:'
            line: "hostname: thankview-{{ app_role_value | lower }}-{{ ansible_ec2_instance_id | default('unknown') | regex_replace('i-', '') | truncate(8, true, '') }}"
            state: present
            create: yes
          when:
            - app_role_value is defined
            - app_role_value != ''
          become: yes

        - name: Update Datadog tags with server role
          lineinfile:
            path: /etc/datadog-agent/datadog.yaml
            regexp: '^  - role:'
            line: "  - role:{{ app_role_value | lower }}"
            state: present
            insertafter: '^tags:'
            create: yes
          when:
            - app_role_value is defined
            - app_role_value != ''
          become: yes

        - name: Restart Datadog agent to apply hostname changes
          systemd:
            name: datadog-agent
            state: restarted
          when:
            - app_role_value is defined
            - app_role_value != ''
          become: yes
          ignore_errors: yes
